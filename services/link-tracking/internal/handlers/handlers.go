package handlers

import (
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"link-tracking/internal/models"
	"link-tracking/internal/service"
)

type LinkHandler struct {
	linkService *service.LinkService
}

// NewLinkHandler creates a new link handler
func NewLinkHandler(linkService *service.LinkService) *LinkHandler {
	return &LinkHandler{linkService: linkService}
}

// CreateLink handles POST /api/v1/links
func (h *LinkHandler) CreateLink(c *gin.Context) {
	var req models.CreateLinkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request format"),
		})
		return
	}

	link, err := h.linkService.CreateLink(c.Request.Context(), &req)
	if err != nil {
		logrus.WithError(err).Error("Failed to create link")
		c.<PERSON>(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to create link"),
		})
		return
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Data:    link,
		Message: stringPtr("Link created successfully"),
	})
}

// ListLinks handles GET /api/v1/links
func (h *LinkHandler) ListLinks(c *gin.Context) {
	logrus.Info("ListLinks handler called")
	// Parse query parameters
	var params models.ListLinksParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid query parameters"),
		})
		return
	}

	// Set default values
	if params.Limit == 0 {
		params.Limit = 20
	}
	if params.Limit > 100 {
		params.Limit = 100
	}

	// Get tenant ID from query or context (in a real app, this would come from auth)
	tenantIDStr := c.Query("tenant_id")
	if tenantIDStr == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("tenant_id is required"),
		})
		return
	}

	tenantID, err := uuid.Parse(tenantIDStr)
	if err != nil {
		logrus.WithError(err).WithField("tenant_id_str", tenantIDStr).Error("Failed to parse tenant ID")
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid tenant ID"),
		})
		return
	}
	
	logrus.WithFields(logrus.Fields{
		"tenant_id": tenantID,
		"params": params,
	}).Info("Listing links for tenant")

	response, err := h.linkService.ListLinks(c.Request.Context(), tenantID, &params)
	if err != nil {
		logrus.WithError(err).Error("Failed to list links")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to list links"),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    response,
	})
}

// GetLink handles GET /api/v1/links/:id
func (h *LinkHandler) GetLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid link ID"),
		})
		return
	}

	link, err := h.linkService.GetLink(c.Request.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Success: false,
				Error:   stringPtr("Link not found"),
			})
			return
		}

		logrus.WithError(err).Error("Failed to get link")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to get link"),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    link,
	})
}

// UpdateLink handles PUT /api/v1/links/:id
func (h *LinkHandler) UpdateLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid link ID"),
		})
		return
	}

	var req models.UpdateLinkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request format"),
		})
		return
	}

	link, err := h.linkService.UpdateLink(c.Request.Context(), id, &req)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Success: false,
				Error:   stringPtr("Link not found"),
			})
			return
		}

		logrus.WithError(err).Error("Failed to update link")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to update link"),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    link,
		Message: stringPtr("Link updated successfully"),
	})
}

// DeleteLink handles DELETE /api/v1/links/:id
func (h *LinkHandler) DeleteLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid link ID"),
		})
		return
	}

	err = h.linkService.DeleteLink(c.Request.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Success: false,
				Error:   stringPtr("Link not found"),
			})
			return
		}

		logrus.WithError(err).Error("Failed to delete link")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to delete link"),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Message: stringPtr("Link deleted successfully"),
	})
}

// CreateBulkLinks handles POST /api/v1/links/bulk
func (h *LinkHandler) CreateBulkLinks(c *gin.Context) {
	var req struct {
		Links []models.CreateLinkRequest `json:"links" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid request format"),
		})
		return
	}

	if len(req.Links) == 0 {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Links array cannot be empty"),
		})
		return
	}

	// Create links in batch
	var createdLinks []interface{}
	var errors []string

	for i, linkReq := range req.Links {
		link, err := h.linkService.CreateLink(c.Request.Context(), &linkReq)
		if err != nil {
			logrus.WithError(err).WithField("link_index", i).Error("Failed to create bulk link")
			errors = append(errors, err.Error())
			continue
		}
		createdLinks = append(createdLinks, link)
	}

	if len(errors) > 0 && len(createdLinks) == 0 {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to create any links"),
		})
		return
	}

	response := map[string]interface{}{
		"created_links": createdLinks,
		"created_count": len(createdLinks),
		"total_count":   len(req.Links),
	}

	if len(errors) > 0 {
		response["errors"] = errors
		response["error_count"] = len(errors)
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Success: true,
		Data:    response,
		Message: stringPtr("Bulk links creation completed"),
	})
}

// GenerateQRCode handles POST /api/v1/links/:id/qr-code
func (h *LinkHandler) GenerateQRCode(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid link ID"),
		})
		return
	}

	var req struct {
		Size   int    `json:"size"`
		Format string `json:"format"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		// Set defaults if no body provided
		req.Size = 200
		req.Format = "png"
	}

	// Validate parameters
	if req.Size <= 0 || req.Size > 1000 {
		req.Size = 200
	}
	if req.Format != "png" && req.Format != "jpg" && req.Format != "svg" {
		req.Format = "png"
	}

	// Get the link first to ensure it exists
	link, err := h.linkService.GetLink(c.Request.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Success: false,
				Error:   stringPtr("Link not found"),
			})
			return
		}

		logrus.WithError(err).Error("Failed to get link for QR code")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to get link"),
		})
		return
	}

	// For now, return a placeholder response
	// In a real implementation, you would generate the actual QR code
	qrData := map[string]interface{}{
		"link_id":    link.ID,
		"short_url":  link.ShortCode, // You'll need to construct the full URL
		"qr_code":    "data:image/png;base64,placeholder", // Placeholder
		"size":       req.Size,
		"format":     req.Format,
		"generated_at": time.Now(),
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    qrData,
		Message: stringPtr("QR code generated successfully"),
	})
}

// HandleRedirect handles GET /:code (link redirection)
func (h *LinkHandler) HandleRedirect(c *gin.Context) {
	shortCode := c.Param("code")
	if shortCode == "" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid short code"),
		})
		return
	}

	// Extract click information
	ipAddress := getClientIP(c)
	userAgent := c.GetHeader("User-Agent")
	referrer := c.GetHeader("Referer")

	// Handle the click
	link, err := h.linkService.HandleClick(c.Request.Context(), shortCode, ipAddress, userAgent, referrer)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Success: false,
				Error:   stringPtr("Link not found"),
			})
			return
		}

		if strings.Contains(err.Error(), "expired") {
			c.JSON(http.StatusGone, models.APIResponse{
				Success: false,
				Error:   stringPtr("Link has expired"),
			})
			return
		}

		logrus.WithError(err).Error("Failed to handle click")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to process click"),
		})
		return
	}

	// Redirect to target URL
	c.Redirect(http.StatusFound, link.TargetURL)
}

// GetLinkAnalytics handles GET /api/v1/links/:id/analytics
func (h *LinkHandler) GetLinkAnalytics(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Success: false,
			Error:   stringPtr("Invalid link ID"),
		})
		return
	}

	analytics, err := h.linkService.GetLinkAnalytics(c.Request.Context(), id)
	if err != nil {
		logrus.WithError(err).Error("Failed to get link analytics")
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Success: false,
			Error:   stringPtr("Failed to get analytics"),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Success: true,
		Data:    analytics,
	})
}

// HealthCheck handles GET /health
func HealthCheck(c *gin.Context) {
	response := models.HealthResponse{
		Status:    "healthy",
		Service:   "link-tracking",
		Timestamp: time.Now().Format(time.RFC3339),
		Version:   "1.0.0",
	}

	c.JSON(http.StatusOK, response)
}

// ReadinessCheck handles GET /ready
func ReadinessCheck(c *gin.Context) {
	// In a real implementation, you'd check dependencies here
	// For now, just return ready
	response := models.HealthResponse{
		Status:    "ready",
		Service:   "link-tracking",
		Timestamp: time.Now().Format(time.RFC3339),
		Version:   "1.0.0",
	}

	c.JSON(http.StatusOK, response)
}

// CORSMiddleware adds CORS headers
func CORSMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})
}

// getClientIP extracts the client IP address from the request
func getClientIP(c *gin.Context) string {
	// Check X-Forwarded-For header first (for load balancers)
	forwarded := c.GetHeader("X-Forwarded-For")
	if forwarded != "" {
		// X-Forwarded-For can contain multiple IPs, get the first one
		ips := strings.Split(forwarded, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if net.ParseIP(ip) != nil {
				return ip
			}
		}
	}

	// Check X-Real-IP header
	realIP := c.GetHeader("X-Real-IP")
	if realIP != "" {
		if net.ParseIP(realIP) != nil {
			return realIP
		}
	}

	// Fall back to remote address
	ip, _, err := net.SplitHostPort(c.Request.RemoteAddr)
	if err != nil {
		return c.Request.RemoteAddr
	}

	return ip
}

// stringPtr returns a pointer to the given string
func stringPtr(s string) *string {
	return &s
}